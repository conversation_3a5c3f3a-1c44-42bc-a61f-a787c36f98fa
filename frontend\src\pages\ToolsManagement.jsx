import { useSelector } from 'react-redux';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import ToolList from '../components/tools/ToolList';
import BulkImportTools from '../components/tools/BulkImportTools';

const ToolsManagement = () => {
  const { user } = useSelector((state) => state.auth);
  const location = useLocation();
  const isAdmin = user?.is_admin || user?.department === 'Materials';
  const unauthorized = location.state?.unauthorized;

  return (
    <div className="w-100">
      {/* Show unauthorized message if redirected from admin page */}
      {unauthorized && (
        <Alert variant="danger" className="mb-4">
          <Alert.Heading>Access Denied</Alert.Heading>
          <p>
            You do not have permission to access the Admin Dashboard. This area is restricted to administrators only.
          </p>
        </Alert>
      )}

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-4 gap-3">
        <h1 className="mb-0">Tool Inventory</h1>
        <div className="d-flex gap-2">
          {isAdmin && (
            <Button as={Link} to="/calibrations" variant="outline-primary" size="lg">
              <i className="bi bi-tools me-2"></i>
              Calibration Management
            </Button>
          )}
          {isAdmin && <BulkImportTools />}
          {isAdmin && (
            <Button as={Link} to="/tools/new" variant="success" size="lg">
              <i className="bi bi-plus-circle me-2"></i>
              Add New Tool
            </Button>
          )}
        </div>
      </div>

      <ToolList />
    </div>
  );
};

export default ToolsManagement;
