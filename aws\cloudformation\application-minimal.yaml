AWSTemplateFormatVersion: '2010-09-09'
Description: 'SupplyLine MRO Suite - Minimal Application Deployment'

Parameters:
  InfrastructureStackName:
    Type: String
    Description: Name of the infrastructure CloudFormation stack
    Default: supplyline-mro-suite-infra-v3
  
  Environment:
    Type: String
    Default: production
    Description: Environment name
  
  BackendImageUri:
    Type: String
    Description: ECR URI for backend Docker image
    Default: 236224546224.dkr.ecr.us-east-1.amazonaws.com/supplyline-mro-suite-backend:latest

  DatabasePassword:
    Type: String
    NoEcho: true
    Description: Database password (must match infrastructure stack)

  JWTSecretKey:
    Type: String
    NoEcho: true
    MinLength: 32
    Description: JWT secret key for token signing (minimum 32 characters)

  AppSecretKey:
    Type: String
    NoEcho: true
    MinLength: 32
    Description: Application secret key for session management (minimum 32 characters)

Resources:
  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub '${AWS::StackName}-cluster'
      CapacityProviders:
        - FARGATE
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1

  # IAM Roles
  ECSExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy

  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole

  # CloudWatch Log Group
  BackendLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/ecs/${AWS::StackName}-backend'
      RetentionInDays: 30

  # ECS Task Definition
  BackendTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${AWS::StackName}-backend'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 512
      Memory: 1024
      ExecutionRoleArn: !Ref ECSExecutionRole
      TaskRoleArn: !Ref ECSTaskRole
      ContainerDefinitions:
        - Name: backend
          Image: !Ref BackendImageUri
          Essential: true
          PortMappings:
            - ContainerPort: 5000
              Protocol: tcp
          Environment:
            - Name: FLASK_ENV
              Value: !Ref Environment
            - Name: DB_HOST
              Value:
                Fn::ImportValue: !Sub '${InfrastructureStackName}-DatabaseEndpoint'
            - Name: DB_PORT
              Value:
                Fn::ImportValue: !Sub '${InfrastructureStackName}-DatabasePort'
            - Name: DB_NAME
              Value: supplyline
            - Name: DB_USER
              Value: supplyline_admin
            - Name: DB_PASSWORD
              Value: !Ref DatabasePassword
            - Name: JWT_SECRET_KEY
              Value: !Ref JWTSecretKey
            - Name: SECRET_KEY
              Value: !Ref AppSecretKey
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref BackendLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: backend

  # ECS Service (without load balancer for now)
  BackendService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${AWS::StackName}-backend-service'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref BackendTaskDefinition
      DesiredCount: 1
      LaunchType: FARGATE
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - Fn::ImportValue: !Sub '${InfrastructureStackName}-ApplicationSecurityGroup'
          Subnets:
            - Fn::ImportValue: !Sub '${InfrastructureStackName}-PublicSubnet1Id'
            - Fn::ImportValue: !Sub '${InfrastructureStackName}-PublicSubnet2Id'
          AssignPublicIp: ENABLED
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50

Outputs:
  ECSClusterName:
    Description: ECS Cluster Name
    Value: !Ref ECSCluster
    Export:
      Name: !Sub '${AWS::StackName}-ECSCluster'

  BackendServiceArn:
    Description: ECS Backend Service ARN
    Value: !Ref BackendService
    Export:
      Name: !Sub '${AWS::StackName}-BackendServiceArn'

  BackendTaskDefinitionArn:
    Description: Backend Task Definition ARN
    Value: !Ref BackendTaskDefinition
    Export:
      Name: !Sub '${AWS::StackName}-BackendTaskDefinition'
