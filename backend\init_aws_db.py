#!/usr/bin/env python3
"""
Quick AWS Database Initialization Script
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set environment variables
os.environ['DB_HOST'] = 'supplyline-mro-suite-infra-v3-postgres.c2las2g82og9.us-east-1.rds.amazonaws.com'
os.environ['DB_PORT'] = '5432'
os.environ['DB_USER'] = 'supplyline_admin'
os.environ['DB_PASSWORD'] = 'SupplyLine2024SecureDB'
os.environ['FLASK_ENV'] = 'production'

def create_database():
    """Create the supplyline database if it doesn't exist"""
    try:
        # Connect to PostgreSQL server (default postgres database)
        conn = psycopg2.connect(
            host=os.environ['DB_HOST'],
            port=os.environ['DB_PORT'],
            database='postgres',
            user=os.environ['DB_USER'],
            password=os.environ['DB_PASSWORD']
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Check if supplyline database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname='supplyline'")
        exists = cursor.fetchone()

        if not exists:
            print('Creating supplyline database...')
            cursor.execute('CREATE DATABASE supplyline')
            print('Database created successfully!')
        else:
            print('Database supplyline already exists')

        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error creating database: {e}")
        return False

def test_connection():
    """Test connection to the supplyline database"""
    try:
        # Connect to the supplyline database
        conn = psycopg2.connect(
            host=os.environ['DB_HOST'],
            port=os.environ['DB_PORT'],
            database='supplyline',
            user=os.environ['DB_USER'],
            password=os.environ['DB_PASSWORD']
        )
        cursor = conn.cursor()
        cursor.execute('SELECT version()')
        version = cursor.fetchone()
        print(f'Connected to: {version[0]}')
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error connecting to supplyline database: {e}")
        return False

if __name__ == '__main__':
    print("Starting AWS database initialization...")
    
    if create_database():
        print("Database creation successful!")
        
        if test_connection():
            print("Database connection test successful!")
            print("Ready to initialize tables...")
        else:
            print("Database connection test failed!")
    else:
        print("Database creation failed!")
