{"containerDefinitions": [{"name": "backend", "image": "236224546224.dkr.ecr.us-east-1.amazonaws.com/supplyline-mro-suite-backend:v1.2-health-check", "cpu": 0, "links": [], "portMappings": [{"containerPort": 5000, "hostPort": 5000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "FLASK_ENV", "value": "production"}, {"name": "DB_PORT", "value": "5432"}, {"name": "DB_USER", "value": "supplyline_admin"}, {"name": "SECRET_KEY", "value": "64IIwxJLP0SwR2uWOHyMeD+VvLf2EFily1L6q74c24"}, {"name": "DB_NAME", "value": "supplyline"}, {"name": "CORS_ORIGINS", "value": "http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173,http://supplyline-frontend-236224546224.s3-website-us-east-1.amazonaws.com"}, {"name": "DB_HOST", "value": "supplyline-mro-suite-infra-v3-postgres.c2las2g82og9.us-east-1.rds.amazonaws.com"}, {"name": "JWT_SECRET_KEY", "value": "C5sP0O9maGHoNu3h69Wi8tJnMMzMGJONBhxllgQPwEw"}, {"name": "DB_PASSWORD", "value": "SupplyLine2024SecureDB"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/supplyline-mro-suite-app-simple-backend", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "backend"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "supplyline-mro-suite-app-simple-backend", "taskRoleArn": "arn:aws:iam::236224546224:role/supplyline-mro-suite-app-simple-ECSTaskRole-kpEa3mO1c3jB", "executionRoleArn": "arn:aws:iam::236224546224:role/supplyline-mro-suite-app-simple-ECSExecutionRole-6jvZHkurB48s", "networkMode": "awsvpc", "volumes": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}