# SupplyLine MRO Suite - AWS Production Environment Configuration
# Copy this file to .env.aws and update the values for your AWS deployment

# Application Environment
FLASK_ENV=production
ENVIRONMENT=production

# Security
SECRET_KEY=your-super-secure-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# Database Configuration (PostgreSQL for AWS)
# These will be automatically set by the deployment script
DB_HOST=your-rds-endpoint.region.rds.amazonaws.com
DB_PORT=5432
DB_NAME=supplyline
DB_USER=supplyline_admin
DB_PASSWORD=your-secure-database-password

# CORS Configuration
CORS_ORIGINS=https://your-cloudfront-domain.cloudfront.net,https://your-custom-domain.com

# Session Configuration
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Strict
SESSION_VALIDATE_IP=false

# Account Lockout Settings
MAX_FAILED_ATTEMPTS=5
INITIAL_LOCKOUT_MINUTES=15
LOCKOUT_MULTIPLIER=2
MAX_LOCKOUT_MINUTES=60

# Resource Limits
BACKEND_CPU_LIMIT=1.0
BACKEND_MEMORY_LIMIT=2048M
FRONTEND_CPU_LIMIT=0.5
FRONTEND_MEMORY_LIMIT=512M

# Monitoring and Logging
LOG_LEVEL=INFO
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# AWS Specific Settings
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=your-aws-account-id

# CloudWatch Logging
CLOUDWATCH_LOG_GROUP=/aws/ecs/supplyline-mro-suite
CLOUDWATCH_LOG_STREAM=backend

# Health Check Settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Database Connection Pool Settings (PostgreSQL)
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=30

# Application Settings
ADMIN_EMAIL=<EMAIL>
COMPANY_NAME=Your Company Name
SUPPORT_EMAIL=<EMAIL>

# Feature Flags
ENABLE_CYCLE_COUNTS=false
ENABLE_ADVANCED_REPORTING=true
ENABLE_CHEMICAL_TRACKING=true
ENABLE_TOOL_CALIBRATION=true

# Backup and Maintenance
BACKUP_RETENTION_DAYS=30
MAINTENANCE_WINDOW=sun:04:00-sun:05:00
BACKUP_WINDOW=03:00-04:00

# Performance Settings
CACHE_TIMEOUT=300
SESSION_TIMEOUT=28800
API_RATE_LIMIT=1000

# Security Headers
ENABLE_HSTS=true
ENABLE_CSP=true
ENABLE_XSS_PROTECTION=true

# SSL/TLS Settings
SSL_REDIRECT=true
SSL_VERIFY=true

# Email Configuration (if using SES)
EMAIL_BACKEND=ses
SES_REGION=us-east-1
FROM_EMAIL=<EMAIL>

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SLACK_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=

# Data Retention
AUDIT_LOG_RETENTION_DAYS=365
SESSION_DATA_RETENTION_DAYS=30
TEMP_FILE_RETENTION_HOURS=24

# API Configuration
API_VERSION=v1
API_TIMEOUT=30
MAX_REQUEST_SIZE=10MB

# Frontend Configuration
VITE_API_URL=https://your-alb-domain.us-east-1.elb.amazonaws.com/api
VITE_APP_NAME=SupplyLine MRO Suite
VITE_APP_VERSION=4.0.0
VITE_ENVIRONMENT=production

# CDN Configuration
CDN_URL=https://your-cloudfront-domain.cloudfront.net
STATIC_URL=https://your-cloudfront-domain.cloudfront.net/static

# Analytics (optional)
ENABLE_ANALYTICS=false
GOOGLE_ANALYTICS_ID=
MIXPANEL_TOKEN=

# Error Reporting (optional)
SENTRY_DSN=
ROLLBAR_TOKEN=

# Development/Debug Settings (should be false in production)
DEBUG=false
TESTING=false
SQLALCHEMY_ECHO=false

# Deployment Information
DEPLOYMENT_DATE=
DEPLOYMENT_VERSION=
GIT_COMMIT_HASH=
BUILD_NUMBER=
