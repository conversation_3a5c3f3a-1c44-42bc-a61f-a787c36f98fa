{"containerDefinitions": [{"name": "backend", "image": "236224546224.dkr.ecr.us-east-1.amazonaws.com/supplyline-backend:latest", "cpu": 0, "links": [], "portMappings": [{"containerPort": 5000, "hostPort": 5000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "SECRET_KEY", "value": "your-app-secret-key-change-in-production"}, {"name": "DATABASE_URL", "value": "postgresql://supplyline_admin:SupplyLine2024!<EMAIL>:5432/postgres"}, {"name": "CORS_ORIGINS", "value": "http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173,http://supplyline-frontend-236224546224.s3-website-us-east-1.amazonaws.com"}, {"name": "FLASK_ENV", "value": "production"}, {"name": "JWT_SECRET_KEY", "value": "your-jwt-secret-key-change-in-production"}, {"name": "INITIAL_ADMIN_PASSWORD", "value": "Freedom2025!"}, {"name": "CORS_ORIGINS", "value": "http://localhost:5173,http://127.0.0.1:5173,http://supplyline-frontend-236224546224.s3-website-us-east-1.amazonaws.com"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/supplyline-application-simple-backend", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "backend"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "supplyline-application-simple-backend", "taskRoleArn": "arn:aws:iam::236224546224:role/supplyline-application-simple-ECSTaskRole-xfPc8dRAEHWn", "executionRoleArn": "arn:aws:iam::236224546224:role/supplyline-application-simple-ECSExecutionRole-vsY4prHm5yeS", "networkMode": "awsvpc", "volumes": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}